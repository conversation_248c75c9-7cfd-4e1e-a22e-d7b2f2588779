#!/bin/bash

# Comprehensive Fix Deployment Script
# Fixes both Hero Carousel position bug and Sections reorder 404 error

set -e  # Exit on any error

echo "🚀 Starting Comprehensive Fix Deployment..."
echo "================================================"

# Configuration
PRODUCTION_SERVER="***********"
PRODUCTION_PATH="/var/www/streamdb_root/data/www/streamdb.online"
LOCAL_PATH="G:/My Websites/Catalogue-Website/the-stream-db/Streaming_DB"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}📋 Fix Summary:${NC}"
echo "1. Database schema fix for carousel position bug"
echo "2. Enhanced debugging for sections reorder API"
echo "3. Route verification and testing"
echo ""

# Step 1: Database Fix
echo -e "${YELLOW}🔧 Step 1: Database Schema Fix${NC}"
echo "This will remove automatic timestamp updates from content table..."

read -p "Do you want to apply the database fix? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Applying database fix..."
    
    # Create SQL script for database fix
    cat > temp_db_fix.sql << 'EOF'
-- CRITICAL FIX: Remove automatic timestamp updates from content table
USE stream_db;

-- Remove the ON UPDATE CURRENT_TIMESTAMP from updated_at column
ALTER TABLE content 
MODIFY COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Verify the change
DESCRIBE content;
EOF

    echo "Database fix SQL created. Please run this on your production database:"
    echo "----------------------------------------"
    cat temp_db_fix.sql
    echo "----------------------------------------"
    
    read -p "Press Enter after you've run the SQL on production database..."
    rm temp_db_fix.sql
    
    echo -e "${GREEN}✅ Database fix applied${NC}"
else
    echo -e "${YELLOW}⚠️ Database fix skipped${NC}"
fi

# Step 2: Deploy Enhanced Route Files
echo -e "${YELLOW}🔧 Step 2: Deploy Enhanced Route Files${NC}"

# Files to deploy
FILES_TO_DEPLOY=(
    "server/routes/sections.js"
)

echo "Files to deploy:"
for file in "${FILES_TO_DEPLOY[@]}"; do
    echo "  - $file"
done

read -p "Deploy enhanced route files to production? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Deploying files..."
    
    # Create backup directory
    ssh root@$PRODUCTION_SERVER "mkdir -p $PRODUCTION_PATH/backups/comprehensive-fix-$(date +%Y%m%d-%H%M%S)"
    
    # Backup existing files
    for file in "${FILES_TO_DEPLOY[@]}"; do
        echo "Backing up $file..."
        ssh root@$PRODUCTION_SERVER "cp $PRODUCTION_PATH/$file $PRODUCTION_PATH/backups/comprehensive-fix-$(date +%Y%m%d-%H%M%S)/"
    done
    
    # Deploy new files
    for file in "${FILES_TO_DEPLOY[@]}"; do
        echo "Deploying $file..."
        scp "$LOCAL_PATH/$file" root@$PRODUCTION_SERVER:$PRODUCTION_PATH/$file
    done
    
    echo -e "${GREEN}✅ Files deployed${NC}"
else
    echo -e "${YELLOW}⚠️ File deployment skipped${NC}"
fi

# Step 3: Restart Production Server
echo -e "${YELLOW}🔧 Step 3: Restart Production Server${NC}"

read -p "Restart the production Node.js server? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Restarting server..."
    ssh root@$PRODUCTION_SERVER "cd $PRODUCTION_PATH && pm2 restart all"
    
    # Wait for server to start
    echo "Waiting for server to start..."
    sleep 5
    
    echo -e "${GREEN}✅ Server restarted${NC}"
else
    echo -e "${YELLOW}⚠️ Server restart skipped${NC}"
fi

# Step 4: Test the Fixes
echo -e "${YELLOW}🔧 Step 4: Test the Fixes${NC}"

echo "Testing sections API..."
curl -s "https://streamdb.online/api/sections/test" | jq '.' || echo "Test endpoint not responding"

echo ""
echo -e "${GREEN}🎉 Deployment Complete!${NC}"
echo ""
echo -e "${BLUE}📋 Next Steps:${NC}"
echo "1. Test Hero Carousel add/remove operations"
echo "2. Test section reordering in Admin Panel"
echo "3. Check PM2 logs for any errors: pm2 logs"
echo "4. Monitor the debug output for sections reorder"
echo ""
echo -e "${YELLOW}⚠️ If issues persist:${NC}"
echo "1. Check PM2 logs: ssh root@$PRODUCTION_SERVER 'pm2 logs'"
echo "2. Test API directly: curl -X PUT https://streamdb.online/api/sections/reorder"
echo "3. Verify database schema: DESCRIBE content;"
echo ""
echo "Deployment script completed at $(date)"
