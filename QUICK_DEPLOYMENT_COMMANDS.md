# 🚀 QUICK DEPLOYMENT COMMANDS - <PERSON><PERSON> & Paste Ready

## **DATABASE FIX (Run on Production Server)**

```bash
# 1. SSH to production
ssh root@45.93.8.197

# 2. Access MySQL
mysql -u root -p

# 3. Run these SQL commands:
USE stream_db;
ALTER TABLE content MODIFY COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
DESCRIBE content;
exit;
```

## **FILE DEPLOYMENT (Run from Local Windows Machine)**

```bash
# 1. Deploy enhanced sections.js file
scp "G:\My Websites\Catalogue-Website\the-stream-db\Streaming_DB\server\routes\sections.js" root@45.93.8.197:/var/www/streamdb_root/data/www/streamdb.online/server/routes/sections.js
```

## **SERVER RESTART (Run on Production Server)**

```bash
# 1. SSH to production (if not already connected)
ssh root@45.93.8.197

# 2. Navigate to app directory
cd /var/www/streamdb_root/data/www/streamdb.online

# 3. Create backup
mkdir -p backups/fix-$(date +%Y%m%d-%H%M%S)
cp server/routes/sections.js backups/fix-$(date +%Y%m%d-%H%M%S)/

# 4. Restart application
pm2 restart all

# 5. Check status
pm2 list
pm2 logs --lines 10
```

## **VERIFICATION TESTS**

```bash
# 1. Test API endpoint
curl -s "https://streamdb.online/api/sections/test"

# 2. Check database fix
mysql -u root -p -e "USE stream_db; DESCRIBE content;" | grep updated_at

# 3. Monitor logs
pm2 logs --lines 20
```

## **EXPECTED RESULTS**

### **Database Check:**
```
updated_at | timestamp | YES | | CURRENT_TIMESTAMP |
```
(Should NOT show "ON UPDATE CURRENT_TIMESTAMP")

### **API Test:**
```json
{"success":true,"message":"Sections API is working","timestamp":"...","environment":"production"}
```

### **PM2 Status:**
```
┌─────┬──────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id  │ name     │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├─────┼──────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0   │ index    │ default     │ 1.0.0   │ fork    │ 12345    │ 2s     │ 0    │ online    │ 0%       │ 50.0mb   │ root     │ disabled │
└─────┴──────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
```

## **TROUBLESHOOTING COMMANDS**

```bash
# If API test fails:
curl -v "https://streamdb.online/api/sections/test"

# If PM2 shows errors:
pm2 logs --lines 50

# If database fix didn't work:
mysql -u root -p -e "USE stream_db; SHOW CREATE TABLE content;"

# Check file permissions:
ls -la /var/www/streamdb_root/data/www/streamdb.online/server/routes/sections.js
chmod 644 /var/www/streamdb_root/data/www/streamdb.online/server/routes/sections.js
```

## **ROLLBACK COMMANDS (Emergency Only)**

```bash
# Rollback code changes:
cp backups/fix-*/sections.js server/routes/sections.js
pm2 restart all

# Rollback database (NOT RECOMMENDED):
mysql -u root -p -e "USE stream_db; ALTER TABLE content MODIFY COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;"
```

---

## **DEPLOYMENT CHECKLIST**

- [ ] SSH access to 45.93.8.197 working
- [ ] Database fix applied and verified
- [ ] Enhanced sections.js file deployed
- [ ] Backup created
- [ ] PM2 restarted successfully
- [ ] API test endpoint responding
- [ ] Hero carousel position test passed
- [ ] Section reordering test passed
- [ ] Content management still works correctly

**Total time: 5-10 minutes | Downtime: ~30 seconds during restart**
