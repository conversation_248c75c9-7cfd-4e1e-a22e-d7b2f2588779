-- CRITICAL FIX: Remove automatic timestamp updates from content table
-- This fixes the Hero Carousel position bug by allowing manual timestamp control

USE stream_db;

-- Step 1: Remove the ON UPDATE CURRENT_TIMESTAMP from updated_at column
ALTER TABLE content 
MODIFY COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Step 2: Verify the change was applied
DESCRIBE content;

-- Step 3: Test that manual updates work
-- UPDATE content SET updated_at = '2025-01-01 00:00:00' WHERE id = 'test_id';
-- This should now work without being overridden by the database

-- IMPORTANT: After running this SQL, the application code will have full control
-- over when updated_at is modified, fixing the carousel position issue.
