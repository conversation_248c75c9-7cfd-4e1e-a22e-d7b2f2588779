Below are the PM2 logs : 


┌────┬────────────────────┬──────────┬──────┬───────────┬──────────┬──────────┐
│ id │ name               │ mode     │ ↺    │ status    │ cpu      │ memory   │
├────┼────────────────────┼──────────┼──────┼───────────┼──────────┼──────────┤
│ 0  │ streamdb-online    │ fork     │ 6831 │ online    │ 0%       │ 84.8mb   │
└────┴────────────────────┴──────────┴──────┴───────────┴──────────┴──────────┘
root@backend1maindb:/var/www/streamdb_root/data/www/streamdb.online# pm2 logs streamdb-online --lines 50
[TAILING] Tailing last 50 lines for [streamdb-online] process (change the value with --lines option)
/root/.pm2/logs/streamdb-online-error.log last 50 lines:
0|streamdb |     at next (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/express/lib/router/index.js:280:10)
0|streamdb |     at /var/www/streamdb_root/data/www/streamdb.online/server/index.js:52:3
0|streamdb | CORS blocked origin: https://akiele-chalupa.eaglevalleyspeedway.net
0|streamdb | Error: Error: Not allowed by CORS
0|streamdb |     at origin (/var/www/streamdb_root/data/www/streamdb.online/server/index.js:100:16)
0|streamdb |     at /var/www/streamdb_root/data/www/streamdb.online/server/node_modules/cors/lib/index.js:219:13
0|streamdb |     at optionsCallback (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/cors/lib/index.js:199:9)
0|streamdb |     at corsMiddleware (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/cors/lib/index.js:204:7)
0|streamdb |     at Layer.handle [as handle_request] (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/express/lib/router/layer.js:95:5)
0|streamdb |     at trim_prefix (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/express/lib/router/index.js:328:13)
0|streamdb |     at /var/www/streamdb_root/data/www/streamdb.online/server/node_modules/express/lib/router/index.js:286:9
0|streamdb |     at Function.process_params (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/express/lib/router/index.js:346:12)
0|streamdb |     at next (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/express/lib/router/index.js:280:10)
0|streamdb |     at /var/www/streamdb_root/data/www/streamdb.online/server/index.js:52:3
0|streamdb | CORS blocked origin: https://akiele-chalupa.eaglevalleyspeedway.net
0|streamdb | Error: Error: Not allowed by CORS
0|streamdb |     at origin (/var/www/streamdb_root/data/www/streamdb.online/server/index.js:100:16)
0|streamdb |     at /var/www/streamdb_root/data/www/streamdb.online/server/node_modules/cors/lib/index.js:219:13
0|streamdb |     at optionsCallback (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/cors/lib/index.js:199:9)
0|streamdb |     at corsMiddleware (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/cors/lib/index.js:204:7)
0|streamdb |     at Layer.handle [as handle_request] (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/express/lib/router/layer.js:95:5)
0|streamdb |     at trim_prefix (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/express/lib/router/index.js:328:13)
0|streamdb |     at /var/www/streamdb_root/data/www/streamdb.online/server/node_modules/express/lib/router/index.js:286:9
0|streamdb |     at Function.process_params (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/express/lib/router/index.js:346:12)
0|streamdb |     at next (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/express/lib/router/index.js:280:10)
0|streamdb |     at /var/www/streamdb_root/data/www/streamdb.online/server/index.js:52:3
0|streamdb | CORS blocked origin: https://akiele-chalupa.eaglevalleyspeedway.net
0|streamdb | Error: Error: Not allowed by CORS
0|streamdb |     at origin (/var/www/streamdb_root/data/www/streamdb.online/server/index.js:100:16)
0|streamdb |     at /var/www/streamdb_root/data/www/streamdb.online/server/node_modules/cors/lib/index.js:219:13
0|streamdb |     at optionsCallback (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/cors/lib/index.js:199:9)
0|streamdb |     at corsMiddleware (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/cors/lib/index.js:204:7)
0|streamdb |     at Layer.handle [as handle_request] (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/express/lib/router/layer.js:95:5)
0|streamdb |     at trim_prefix (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/express/lib/router/index.js:328:13)
0|streamdb |     at /var/www/streamdb_root/data/www/streamdb.online/server/node_modules/express/lib/router/index.js:286:9
0|streamdb |     at Function.process_params (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/express/lib/router/index.js:346:12)
0|streamdb |     at next (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/express/lib/router/index.js:280:10)
0|streamdb |     at /var/www/streamdb_root/data/www/streamdb.online/server/index.js:52:3
0|streamdb | CORS blocked origin: https://akiele-chalupa.eaglevalleyspeedway.net
0|streamdb | Error: Error: Not allowed by CORS
0|streamdb |     at origin (/var/www/streamdb_root/data/www/streamdb.online/server/index.js:100:16)
0|streamdb |     at /var/www/streamdb_root/data/www/streamdb.online/server/node_modules/cors/lib/index.js:219:13
0|streamdb |     at optionsCallback (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/cors/lib/index.js:199:9)
0|streamdb |     at corsMiddleware (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/cors/lib/index.js:204:7)
0|streamdb |     at Layer.handle [as handle_request] (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/express/lib/router/layer.js:95:5)
0|streamdb |     at trim_prefix (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/express/lib/router/index.js:328:13)
0|streamdb |     at /var/www/streamdb_root/data/www/streamdb.online/server/node_modules/express/lib/router/index.js:286:9
0|streamdb |     at Function.process_params (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/express/lib/router/index.js:346:12)
0|streamdb |     at next (/var/www/streamdb_root/data/www/streamdb.online/server/node_modules/express/lib/router/index.js:280:10)
0|streamdb |     at /var/www/streamdb_root/data/www/streamdb.online/server/index.js:52:3

/root/.pm2/logs/streamdb-online-out.log last 50 lines:
0|streamdb |     studio: 'Tips Industries',
0|streamdb |     tags: null,
0|streamdb |     trailer: 'https://www.youtube.com/embed/4tUkrRrXl2Q?si=4Xr9QIXutZKUKSA0',
0|streamdb |     subtitle_url: null,
0|streamdb |     is_published: 1,
0|streamdb |     is_featured: 1,
0|streamdb |     add_to_carousel: 1,
0|streamdb |     carousel_position: null,
0|streamdb |     crop_settings: { x: 50, y: 25, width: 100, height: 100 },
0|streamdb |     total_seasons: 0,
0|streamdb |     total_episodes: 0,
0|streamdb |     languages: [ 'Hindi' ],
0|streamdb |     genres: [ 'Action', 'Thriller', 'Crime', 'Drama' ],
0|streamdb |     quality: [],
0|streamdb |     quality_label: 'HDTS',
0|streamdb |     audio_tracks: [ 'Hindi' ],
0|streamdb |     created_at: 2025-07-31T20:42:34.000Z,
0|streamdb |     updated_at: 2025-08-01T14:24:08.000Z,
0|streamdb |     section_slug: 'movies',
0|streamdb |     section_name: 'Movies'
0|streamdb |   }
0|streamdb | ]
0|streamdb | Result type: object Array? true
0|streamdb | Result[0] type: object Array? false
0|streamdb | Content rows received: 9
0|streamdb | [DEBUG] Parsing genres for content content_1753904939187_2m10g6td1: { value: [ 'Action', 'Drama' ], type: 'object' }
0|streamdb | [DEBUG] Genres already array for content content_1753904939187_2m10g6td1: [ 'Action', 'Drama' ]
0|streamdb | [DEBUG] Parsing genres for content content_1753821273803_hiaghqeje: { value: [ 'Science Fiction', 'Adventure', 'Action' ], type: 'object' }
0|streamdb | [DEBUG] Genres already array for content content_1753821273803_hiaghqeje: [ 'Science Fiction', 'Adventure', 'Action' ]
0|streamdb | [DEBUG] Parsing genres for content content_1753910080348_00bt4m7x4: { value: [ 'Science Fiction', 'Adventure' ], type: 'object' }
0|streamdb | [DEBUG] Genres already array for content content_1753910080348_00bt4m7x4: [ 'Science Fiction', 'Adventure' ]
0|streamdb | [DEBUG] Parsing genres for content content_1753824205444_xxtlqdu9u: { value: [ 'Thriller', 'Science Fiction' ], type: 'object' }
0|streamdb | [DEBUG] Genres already array for content content_1753824205444_xxtlqdu9u: [ 'Thriller', 'Science Fiction' ]
0|streamdb | [DEBUG] Parsing genres for content content_1753991564463_v1pv6cbyb: { value: [ 'Science Fiction', 'Adventure', 'Action' ], type: 'object' }
0|streamdb | [DEBUG] Genres already array for content content_1753991564463_v1pv6cbyb: [ 'Science Fiction', 'Adventure', 'Action' ]
0|streamdb | [DEBUG] Parsing genres for content content_1753636388649_oxlrmt973: {
0|streamdb |   value: [ 'Fantasy', 'Comedy', 'Family', 'Adventure', 'Action' ],
0|streamdb |   type: 'object'
0|streamdb | }
0|streamdb | [DEBUG] Genres already array for content content_1753636388649_oxlrmt973: [ 'Fantasy', 'Comedy', 'Family', 'Adventure', 'Action' ]
0|streamdb | [DEBUG] Parsing genres for content content_1753734217367_wr8uqnhva: { value: [ 'Fantasy', 'Family', 'Action' ], type: 'object' }
0|streamdb | [DEBUG] Genres already array for content content_1753734217367_wr8uqnhva: [ 'Fantasy', 'Family', 'Action' ]
0|streamdb | [DEBUG] Parsing genres for content content_1753903070304_8fbprw028: {
0|streamdb |   value: [ 'Family', 'Science Fiction', 'Comedy', 'Adventure' ],
0|streamdb |   type: 'object'
0|streamdb | }
0|streamdb | [DEBUG] Genres already array for content content_1753903070304_8fbprw028: [ 'Family', 'Science Fiction', 'Comedy', 'Adventure' ]
0|streamdb | [DEBUG] Parsing genres for content content_1753994554871_rihnxodas: { value: [ 'Action', 'Thriller', 'Crime', 'Drama' ], type: 'object' }
0|streamdb | [DEBUG] Genres already array for content content_1753994554871_rihnxodas: [ 'Action', 'Thriller', 'Crime', 'Drama' ]
0|streamdb | 2401:4900:1c17:b0cc:c11a:1801:96b1:55e4 - - [01/Aug/2025:14:25:13 +0000] "GET /api/content?carousel=true&limit=50 HTTP/1.1" 200 - "https://streamdb.online/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"