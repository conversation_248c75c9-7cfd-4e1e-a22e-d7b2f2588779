# 🚨 HERO CAROUSEL & SECTIONS FIX - PRODUCTION DEPLOYMENT

## **CRITICAL: Complete Step-by-Step Production Deployment Guide**

### **📋 DEPLOYMENT OVERVIEW**
- **Server**: *********** (Backend with FastPanel CP + MySQL)
- **Domain**: streamdb.online
- **Path**: `/var/www/streamdb_root/data/www/streamdb.online`
- **Database**: stream_db (MySQL)
- **Process Manager**: PM2
- **Estimated Time**: 5-10 minutes
- **Downtime**: Minimal (30 seconds during restart)

---

## **🔧 STEP 1: DATABASE SCHEMA FIX (CRITICAL - Do This First)**

### **1.1 Access Production Database**
```bash
# SSH to production server
ssh root@***********

# Option A: Use MySQL command line
mysql -u root -p

# Option B: Use phpMyAdmin (if available via FastPanel)
# Check FastPanel control panel for database access
```

### **1.2 Execute Database Fix**
```sql
-- Switch to the correct database
USE stream_db;

-- CRITICAL FIX: Remove automatic timestamp updates
ALTER TABLE content 
MODIFY COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Verify the fix worked (should NOT show "on update CURRENT_TIMESTAMP")
DESCRIBE content;
```

### **1.3 Test Database Fix**
```sql
-- Test manual timestamp control
SELECT id, title, updated_at FROM content LIMIT 1;

-- Note the ID and current timestamp, then test:
UPDATE content SET updated_at = '2025-01-01 00:00:00' WHERE id = 'YOUR_TEST_ID';

-- Check if it stayed at 2025-01-01 (if yes, fix worked)
SELECT id, title, updated_at FROM content WHERE updated_at = '2025-01-01 00:00:00';

-- Reset the test record
UPDATE content SET updated_at = NOW() WHERE id = 'YOUR_TEST_ID';
```

### **1.4 Verify Database Fix Success**
```sql
-- This should show the column WITHOUT "on update CURRENT_TIMESTAMP"
SHOW CREATE TABLE content;

-- Expected result should look like:
-- `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
-- (NOT: `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)
```

---

## **🔧 STEP 2: DEPLOY ENHANCED CODE FILES**

### **2.1 Create Backup on Production**
```bash
# SSH to production server (if not already connected)
ssh root@***********

# Navigate to production directory
cd /var/www/streamdb_root/data/www/streamdb.online

# Create backup directory with timestamp
mkdir -p backups/carousel-sections-fix-$(date +%Y%m%d-%H%M%S)

# Backup the file we're going to change
cp server/routes/sections.js backups/carousel-sections-fix-$(date +%Y%m%d-%H%M%S)/

# Verify backup was created
ls -la backups/carousel-sections-fix-*/
```

### **2.2 Deploy Enhanced sections.js File**
```bash
# From your LOCAL Windows machine, open Command Prompt or PowerShell and run:
scp "G:\My Websites\Catalogue-Website\the-stream-db\Streaming_DB\server\routes\sections.js" root@***********:/var/www/streamdb_root/data/www/streamdb.online/server/routes/sections.js

# Verify the file was uploaded correctly
ssh root@*********** "ls -la /var/www/streamdb_root/data/www/streamdb.online/server/routes/sections.js"

# Check file permissions (should be 644)
ssh root@*********** "chmod 644 /var/www/streamdb_root/data/www/streamdb.online/server/routes/sections.js"
```

---

## **🔧 STEP 3: RESTART PRODUCTION SERVER**

### **3.1 Check Current Status**
```bash
# SSH to production server (if not already connected)
ssh root@***********

# Navigate to production directory
cd /var/www/streamdb_root/data/www/streamdb.online

# Check current PM2 processes
pm2 list

# Check current logs for any existing errors
pm2 logs --lines 20
```

### **3.2 Restart the Application**
```bash
# Restart all PM2 processes
pm2 restart all

# Wait for restart to complete
sleep 5

# Check if restart was successful
pm2 list

# Check logs for startup success
pm2 logs --lines 10
```

---

## **🔧 STEP 4: VERIFY DEPLOYMENT SUCCESS**

### **4.1 Test New API Endpoints**
```bash
# Test the new debug endpoint
curl -s "https://streamdb.online/api/sections/test"

# Expected response:
# {"success":true,"message":"Sections API is working","timestamp":"...","environment":"production"}

# If the above fails, try:
curl -v "https://streamdb.online/api/sections/test"
```

### **4.2 Check Application Health**
```bash
# Monitor logs for any errors
pm2 logs --lines 50

# Look for these success indicators:
# - "Server started successfully" or similar
# - No database connection errors
# - No route registration errors
# - No "EADDRINUSE" or port conflicts
```

### **4.3 Test Database Connection**
```bash
# Quick database connectivity test
mysql -u root -p -e "USE stream_db; SELECT COUNT(*) as content_count FROM content;"

# Should return a number (total content count)
```

---

## **🔧 STEP 5: FUNCTIONAL TESTING**

### **5.1 Test Hero Carousel Fix (Issue 1)**
1. **Open Admin Panel**: https://streamdb.online/admin
2. **Login with your admin credentials**
3. **Go to Hero Carousel Manager**
4. **Note current homepage content positions**
5. **Add existing content to carousel**
6. **Check homepage**: Content should STAY in original section position ✅
7. **Remove content from carousel**
8. **Check homepage**: Content should STILL STAY in original section position ✅

### **5.2 Test Section Reordering Fix (Issue 2)**
1. **Open Admin Panel**: https://streamdb.online/admin
2. **Go to All Sections**
3. **Try to drag and drop sections to reorder**
4. **Should save WITHOUT 404 error** ✅
5. **Check browser console (F12) for any errors**
6. **Verify sections appear in new order on homepage**

### **5.3 Verify Content Management Still Works**
1. **Go to Manage Content**
2. **Edit any content (title, description, etc.)**
3. **Save changes**
4. **Check homepage**: Content should move to position #1 ✅ (this is correct behavior)

---

## **🔧 STEP 6: TROUBLESHOOTING (If Issues Occur)**

### **6.1 Database Issues**
```bash
# Check if database fix was applied correctly
mysql -u root -p -e "USE stream_db; DESCRIBE content;" | grep updated_at

# Should show: updated_at | timestamp | YES | | CURRENT_TIMESTAMP |
# Should NOT show: ON UPDATE CURRENT_TIMESTAMP

# If fix wasn't applied, re-run the ALTER TABLE command
```

### **6.2 Application Issues**
```bash
# Check PM2 logs for detailed errors
pm2 logs --lines 100

# Check if files were deployed correctly
ls -la /var/www/streamdb_root/data/www/streamdb.online/server/routes/sections.js

# Check file permissions and ownership
chmod 644 /var/www/streamdb_root/data/www/streamdb.online/server/routes/sections.js
chown www-data:www-data /var/www/streamdb_root/data/www/streamdb.online/server/routes/sections.js
```

### **6.3 API Issues**
```bash
# Test sections API with verbose output
curl -v "https://streamdb.online/api/sections/test"

# Check if route is accessible
curl -I "https://streamdb.online/api/sections/test"

# Test with different HTTP methods
curl -X GET "https://streamdb.online/api/sections/test"
```

### **6.4 Network/SSL Issues**
```bash
# Test without SSL
curl -k "https://streamdb.online/api/sections/test"

# Test direct server access (if applicable)
curl "http://***********:3000/api/sections/test"
```

---

## **🔧 STEP 7: ROLLBACK PLAN (Emergency Only)**

### **7.1 Rollback Database Changes (NOT RECOMMENDED)**
```sql
-- ONLY if you absolutely need to rollback database changes
USE stream_db;
ALTER TABLE content 
MODIFY COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
```

### **7.2 Rollback Code Changes**
```bash
# Restore from backup
cp backups/carousel-sections-fix-*/sections.js server/routes/sections.js
pm2 restart all
```

---

## **✅ SUCCESS INDICATORS**

After successful deployment, you should see:

1. **Database**: `DESCRIBE content` shows NO "ON UPDATE CURRENT_TIMESTAMP"
2. **API**: `curl https://streamdb.online/api/sections/test` returns success JSON
3. **Carousel**: Adding/removing content doesn't change homepage position
4. **Sections**: Reordering sections works without 404 error
5. **Content**: Editing via "Manage Content" still moves content to position #1
6. **Logs**: PM2 logs show no errors, server starts successfully

---

## **📞 IMMEDIATE SUPPORT**

If you encounter any issues during deployment, provide:

1. **PM2 logs**: `pm2 logs --lines 50`
2. **Database status**: `DESCRIBE content;`
3. **API test results**: `curl https://streamdb.online/api/sections/test`
4. **Exact error messages** and which step failed
5. **Browser console errors** (F12 → Console tab)

**Total deployment time should be 5-10 minutes with minimal downtime.**
