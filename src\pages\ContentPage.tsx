import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, <PERSON>, useNavigate, useLocation } from 'react-router-dom';
import { ArrowLeft, Calendar, Clock, Star, Play, Share2, List, ChevronDown, ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import SecureVideoPlayer from '@/components/SecureVideoPlayer';
import AllSeasonsModal from '@/components/AllSeasonsModal';
import { MediaItem } from '@/types/media';
import { scrollToTop } from '@/utils/scrollToTop';
import apiService from '@/services/apiService';

export default function ContentPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const [content, setContent] = useState<MediaItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAllSeasonsModalOpen, setIsAllSeasonsModalOpen] = useState(false);
  const [selectedEpisodeVideoLinks, setSelectedEpisodeVideoLinks] = useState<string>("");
  const [selectedEpisodeTitle, setSelectedEpisodeTitle] = useState<string>("");
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);
  const [selectedSeasonId, setSelectedSeasonId] = useState<string>("");
  const [selectedEpisodeId, setSelectedEpisodeId] = useState<string>("");

  // Smart Go Back function that determines the best navigation path
  const handleGoBack = () => {
    // First, try to use React Router's built-in navigation history
    // This works for client-side navigation within the React app
    if (window.history.length > 1) {
      // Check if we have location state that indicates where we came from
      const fromState = location.state?.from;
      if (fromState) {
        navigate(fromState);
        scrollToTop();
        return;
      }
      
      // Try to go back in history, but with intelligent fallbacks
      try {
        navigate(-1);
        scrollToTop();
        return;
      } catch (error) {
        console.warn('Navigate back failed, using fallback:', error);
      }
    }
    
    // Fallback logic based on content type and intelligent guessing
    if (content) {
      // If it's a movie, try to go to movies page
      if (content.type === 'movie') {
        navigate('/movies');
        scrollToTop();
        return;
      }
      
      // If it's a series, try to go to series page
      if (content.type === 'series') {
        navigate('/series');
        scrollToTop();
        return;
      }
      
      // If content has a category, try to go to that category
      if (content.category) {
        const categorySlug = content.category.toLowerCase().replace(/\s+/g, '-');
        navigate(`/category/${categorySlug}`);
        scrollToTop();
        return;
      }
    }
    
    // Final fallback - go to home page
    navigate('/');
    scrollToTop();
  };

  // Handle episode selection from modal
  const handleEpisodeSelect = (videoLinks: string, episodeTitle: string) => {
    setSelectedEpisodeVideoLinks(videoLinks);
    setSelectedEpisodeTitle(episodeTitle);
    // Scroll to video player after episode selection
    scrollToVideoPlayer();
  };

  // Handle share functionality
  const handleShare = async () => {
    const shareData = {
      title: content?.title || 'Check out this content',
      text: content?.description || 'Amazing content to watch!',
      url: window.location.href,
    };

    try {
      if (navigator.share && navigator.canShare && navigator.canShare(shareData)) {
        await navigator.share(shareData);
      } else {
        // Fallback: copy to clipboard
        await navigator.clipboard.writeText(window.location.href);
        // You could add a toast notification here
        alert('Link copied to clipboard!');
      }
    } catch (error) {
      console.error('Error sharing:', error);
      // Fallback: copy to clipboard
      try {
        await navigator.clipboard.writeText(window.location.href);
        alert('Link copied to clipboard!');
      } catch (clipboardError) {
        console.error('Clipboard error:', clipboardError);
        // Final fallback: show URL in prompt
        prompt('Copy this link:', window.location.href);
      }
    }
  };

  // Handle season selection
  const handleSeasonSelect = (seasonId: string) => {
    setSelectedSeasonId(seasonId);
    setSelectedEpisodeId(""); // Reset episode selection when season changes
    setSelectedEpisodeVideoLinks("");
    setSelectedEpisodeTitle("");
  };

  // Handle episode selection from dropdown
  const handleEpisodeSelectFromDropdown = (episodeId: string) => {
    setSelectedEpisodeId(episodeId);

    // Find the selected episode and its video links
    const selectedSeason = content?.seasons?.find(season => season.id === selectedSeasonId);
    const selectedEpisode = selectedSeason?.episodes?.find(episode => episode.id === episodeId);

    if (selectedEpisode) {
      const videoLinks = selectedEpisode.secure_video_links || selectedEpisode.secureVideoLinks;
      const seasonNum = selectedSeason?.season_number || selectedSeason?.seasonNumber;
      const episodeNum = selectedEpisode.episode_number || selectedEpisode.episode;
      const episodeTitle = `S${seasonNum}E${episodeNum}: ${selectedEpisode.title}`;

      if (videoLinks) {
        setSelectedEpisodeVideoLinks(videoLinks);
        setSelectedEpisodeTitle(episodeTitle);
        scrollToVideoPlayer();
      }
    }
  };

  // Function to scroll to video player
  const scrollToVideoPlayer = () => {
    const videoPlayerElement = document.querySelector('.video-player-container');
    if (videoPlayerElement) {
      videoPlayerElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  useEffect(() => {
    const fetchContent = async () => {
      if (!id) {
        setError('No content ID provided');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const response = await apiService.getContentById(id);

        if (response && response.success && response.data) {
          const contentData = response.data;
          
          // If it's a web series, also fetch seasons and episodes
          if (contentData.type === 'series') {
            try {
              const seasonsResponse = await apiService.getContentSeasons(id);
              if (seasonsResponse && seasonsResponse.success && seasonsResponse.data) {
                contentData.seasons = seasonsResponse.data;
                contentData.totalSeasons = seasonsResponse.data.length;
                contentData.totalEpisodes = seasonsResponse.data.reduce((total, season) => total + (season.episodes?.length || 0), 0);
              }
            } catch (seasonsError) {
              console.error('Error fetching seasons:', seasonsError);
              // Don't fail the whole page if seasons fail to load
              contentData.seasons = [];
            }
          }
          
          setContent(contentData);

          // Auto-select first season if it's a series
          if (contentData.type === 'series' && contentData.seasons && contentData.seasons.length > 0) {
            setSelectedSeasonId(contentData.seasons[0].id);
          }
        } else {
          setContent(null);
          setError('Content not found');
        }
      } catch (error) {
        console.error('Error fetching content:', error);
        setError('Failed to load content');
        setContent(null);
      } finally {
        setLoading(false);
      }
    };

    fetchContent();
  }, [id]);

  if (loading) {
    return (
      <div className="flex flex-col min-h-screen bg-background">
        <Header />
        <main className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md mx-auto px-4">
            <div className="text-6xl mb-4">🎬</div>
            <h1 className="text-3xl font-bold mb-4 text-primary">Loading Content</h1>
            <p className="text-muted-foreground mb-6 leading-relaxed">
              Please wait while we load the content details...
            </p>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (error || !content) {
    return (
      <div className="flex flex-col min-h-screen bg-background">
        <Header />
        <main className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md mx-auto px-4">
            <div className="text-6xl mb-4">🎬</div>
            <h1 className="text-3xl font-bold mb-4 text-primary">Content Not Found</h1>
            <p className="text-muted-foreground mb-6 leading-relaxed">
              {error || "The content you're looking for doesn't exist or may have been removed."}
              Let's get you back to discovering great movies and series!
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Link
                to="/"
                onClick={scrollToTop}
                className="inline-flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Home
              </Link>
              <Link
                to="/movies"
                onClick={scrollToTop}
                className="inline-flex items-center gap-2 px-4 py-2 border border-border rounded-md hover:bg-muted transition-colors"
              >
                Browse Movies
              </Link>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Simulate secure video links (in production, this would come from the database)
  const hasVideoLinks = content.videoLinks || content.secureVideoLinks;

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <Header />
      
      <main className="flex-1">
        {/* Hero Section - Fixed mobile poster positioning */}
        <div className="relative h-[50vh] sm:h-[60vh] md:h-[70vh] min-h-[450px] sm:min-h-[500px] md:min-h-[550px] overflow-hidden"
             style={{
               // Ensure enough space for mobile poster positioning
               minHeight: "clamp(450px, 60vh, 600px)"
             }}>
          <img
            src={content.coverImage || content.image || '/placeholder-image.jpg'}
            alt={content.title || 'Content'}
            className="absolute inset-0 w-full h-full object-cover brightness-[0.35] scale-105 transition-transform duration-700 hover:scale-100"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-background via-background/60 to-background/20" />
          <div className="absolute inset-0 bg-gradient-to-r from-background/40 via-transparent to-background/40" />

          <div className="relative z-10 h-full flex items-center sm:items-end">
            <div className="w-full mx-auto px-4 sm:px-6 md:px-8 py-8 sm:py-12 md:py-16 md:max-w-7xl"
                 style={{
                   // Mobile: Full width with padding for mobile fixes
                   maxWidth: "calc(100vw - 2rem)",
                   margin: "0 auto",
                   boxSizing: "border-box",
                   overflow: "hidden"
                 }}
                 className="hero-section-container">
              <div className="flex flex-col md:flex-row gap-4 sm:gap-6 md:gap-8 items-center md:items-start content-page-hero"
                   style={{
                     // Prevent any overflow from the hero content
                     maxWidth: "100%",
                     overflow: "hidden",
                     boxSizing: "border-box"
                   }}>
                {/* Poster - Fixed Issue #1: Mobile bleeding and positioning - Cross-browser compatible */}
                <div className="flex-shrink-0 group mx-auto md:mx-0 content-page-poster-container mt-0 sm:mt-0 md:mt-0"
                     style={{
                       // Ensure consistent positioning across all browsers
                       display: "flex",
                       justifyContent: "center",
                       alignItems: "center",
                       minHeight: "fit-content"
                     }}>
                  <div className="relative"
                       style={{
                         // Prevent bleeding with explicit container constraints
                         maxWidth: "100%",
                         overflow: "hidden"
                       }}>
                    <img
                      src={content.image}
                      alt={content.title}
                      className="content-page-poster w-36 h-56 sm:w-44 sm:h-68 md:w-52 md:h-80 rounded-xl sm:rounded-2xl shadow-2xl transition-all duration-500 group-hover:shadow-[0_25px_50px_rgba(230,203,142,0.3)] group-hover:scale-105"
                      style={{
                        boxShadow: "0 20px 40px rgba(0, 0, 0, 0.8), 0 8px 16px rgba(230, 203, 142, 0.2)",
                        // Cross-browser object-fit support
                        objectFit: "cover",
                        objectPosition: "center center",
                        // Ensure consistent sizing across browsers
                        width: "clamp(9rem, 15vw, 13rem)",
                        height: "clamp(14rem, 23vw, 20rem)",
                        maxWidth: "100%",
                        maxHeight: "100%"
                      }}
                    />
                    <div className="absolute inset-0 rounded-xl sm:rounded-2xl bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>
                </div>
                
                {/* Content Info - Proper containerization */}
                <div className="flex-1 text-white text-center md:text-left"
                     style={{
                       // Ensure proper containerization and prevent overflow
                       width: "100%",
                       maxWidth: "100%",
                       minWidth: 0, // Allow flex item to shrink
                       overflow: "hidden",
                       boxSizing: "border-box",
                       paddingLeft: "0.5rem",
                       paddingRight: "0.5rem"
                     }}>
                  <div className="mb-4">
                    <Badge
                      variant="secondary"
                      className="mb-3 bg-primary/20 text-primary border-primary/30 font-koulen uppercase tracking-wider text-xs sm:text-sm"
                    >
                      {content.type === 'movie' ? 'Movie' : 'Web Series'}
                    </Badge>
                    <h1 className="stdb-heading mb-2 leading-tight mobile-hero-title" /* Fixed: Dynamic font sizing to fit in one line */
                        style={{
                          color: "#e6cb8e",
                          fontFamily: "'Koulen', Impact, Arial, sans-serif",
                          fontWeight: 500,
                          letterSpacing: "0.015em",
                          textTransform: "uppercase",
                          textShadow: "0 3px 20px rgba(230, 203, 142, 0.4), 0 2px 8px rgba(0, 0, 0, 0.8), 0 1px 3px rgba(0, 0, 0, 0.9)",
                          // Dynamic font sizing based on title length - single line only
                          fontSize: content.title.length <= 15 ? "clamp(1.25rem, 5vw, 2rem)" :
                                   content.title.length <= 25 ? "clamp(1rem, 4vw, 1.5rem)" :
                                   content.title.length <= 35 ? "clamp(0.875rem, 3.5vw, 1.25rem)" :
                                   "clamp(0.75rem, 3vw, 1rem)",
                          whiteSpace: "nowrap",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          maxWidth: "100%",
                          lineHeight: 1.2
                        }}>
                      {content.title}
                    </h1>
                    <div className="w-full mx-auto md:mx-0"
                         style={{
                           // Proper description container
                           maxWidth: "100%",
                           overflow: "hidden",
                           boxSizing: "border-box"
                         }}>
                      <p className={`text-xs sm:text-sm md:text-base lg:text-lg text-gray-100 leading-relaxed font-manrope mobile-hero-description transition-all duration-300 ${
                        isDescriptionExpanded 
                          ? 'line-clamp-none' 
                          : 'line-clamp-1 overflow-hidden text-ellipsis'
                      }`}
                         style={{ textShadow: "0 1px 3px rgba(0, 0, 0, 0.8)" }}>
                        {content.description}
                      </p>
                      {content.description && content.description.length > 100 && (
                        <button
                          onClick={() => setIsDescriptionExpanded(!isDescriptionExpanded)}
                          className="mt-2 text-primary hover:text-primary/80 text-xs sm:text-sm font-medium flex items-center gap-1 transition-colors"
                        >
                          {isDescriptionExpanded ? (
                            <>
                              <ChevronUp className="w-3 h-3 sm:w-4 sm:h-4" />
                              Show Less
                            </>
                          ) : (
                            <>
                              <ChevronDown className="w-3 h-3 sm:w-4 sm:h-4" />
                              Read More
                            </>
                          )}
                        </button>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap gap-3 sm:gap-4 md:gap-6 mb-6 sm:mb-8 justify-center md:justify-start">
                    <div className="flex items-center gap-2 bg-black/30 backdrop-blur-sm px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg border border-white/10">
                      <Calendar className="w-3 h-3 sm:w-4 sm:h-4 text-primary" />
                      <span className="font-medium text-sm sm:text-base">{content.year}</span>
                    </div>
                    {content.runtime && (
                      <div className="flex items-center gap-2 bg-black/30 backdrop-blur-sm px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg border border-white/10">
                        <Clock className="w-3 h-3 sm:w-4 sm:h-4 text-primary" />
                        <span className="font-medium text-sm sm:text-base">{content.runtime} min</span>
                      </div>
                    )}
                    {content.imdbRating && (
                      <div className="flex items-center gap-2 bg-black/30 backdrop-blur-sm px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg border border-white/10">
                        <Star className="w-3 h-3 sm:w-4 sm:h-4 text-yellow-400 fill-yellow-400" />
                        <span className="font-medium text-sm sm:text-base">{content.imdbRating}</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex flex-wrap gap-3 mb-8">
                    {content.genres.map((genre) => (
                      <Badge
                        key={genre}
                        variant="outline"
                        className="text-white border-primary/50 bg-primary/10 backdrop-blur-sm hover:bg-primary/20 hover:border-primary transition-all duration-300 font-medium px-3 py-1 mobile-badge"
                      >
                        {genre}
                      </Badge>
                    ))}
                  </div>
                  
                  <div className="flex flex-wrap gap-2 sm:gap-3 mb-12 sm:mb-16 md:mb-20"
                       style={{
                         // Cross-browser spacing fix to prevent overlap with Episodes section
                         marginBottom: "clamp(3rem, 8vw, 5rem)",
                         paddingBottom: "1rem",
                         // Ensure proper z-index layering
                         position: "relative",
                         zIndex: 10,
                         // Proper containerization
                         width: "100%",
                         maxWidth: "100%",
                         overflow: "hidden",
                         boxSizing: "border-box",
                         justifyContent: "center"
                       }}> {/* Fixed Issue #2: Enhanced spacing to prevent overlap with Episodes section */}
                    {content.type === 'series' && (content.totalSeasons > 0 || (content.seasons && content.seasons.length > 0)) && (
                      <Button
                        size="sm" /* Fixed Issue #2: Reduced from "default" to "sm" (20% smaller) */
                        onClick={() => setIsAllSeasonsModalOpen(true)}
                        className="bg-secondary hover:bg-secondary/90 text-secondary-foreground font-koulen uppercase tracking-wider px-3 py-1.5 sm:px-4 sm:py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 text-xs sm:text-sm" /* Fixed Issue #2: Reduced padding and font size for mobile */
                      >
                        <List className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2" /* Fixed Issue #2: Reduced icon size for mobile */ />
                        All Seasons
                      </Button>
                    )}
                    {hasVideoLinks && (
                      <Button
                        size="sm" /* Fixed Issue #2: Reduced from "default" to "sm" (20% smaller) */
                        onClick={scrollToVideoPlayer}
                        className="bg-primary hover:bg-primary/90 text-primary-foreground font-koulen uppercase tracking-wider px-3 py-1.5 sm:px-4 sm:py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 text-xs sm:text-sm" /* Fixed Issue #2: Reduced padding and font size for mobile */
                      >
                        <Play className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2 fill-current" /* Fixed Issue #2: Reduced icon size for mobile */ />
                        Watch Now
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="sm" /* Fixed Issue #2: Reduced from "default" to "sm" (20% smaller) */
                      onClick={handleShare}
                      className="text-white border-white/40 hover:bg-white/10 backdrop-blur-sm font-medium px-3 py-1.5 sm:px-4 sm:py-2 rounded-xl transition-all duration-300 hover:border-primary/50 hover:text-primary text-xs sm:text-sm" /* Fixed Issue #2: Reduced padding and font size for mobile */
                    >
                      <Share2 className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2" /* Fixed Issue #2: Reduced icon size for mobile */ />
                      Share
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 py-6 sm:py-8 md:py-12 lg:py-16">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8 lg:gap-10">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-4 sm:space-y-6 md:space-y-8 lg:space-y-10">
              {/* Episodes (for series) - Moved above video player */}
              {content.type === 'series' && content.seasons && content.seasons.length > 0 && (
                <Card className="content-page-card rounded-2xl border-border/50 shadow-xl bg-card/95 backdrop-blur-sm hover:shadow-2xl transition-all duration-300">
                  <CardContent className="p-4 sm:p-6 lg:p-8"> {/* Enhanced padding for better mobile spacing */}
                    <h2 className="stdb-heading text-2xl mb-4 sm:mb-6" /* Added responsive margin */
                        style={{
                          color: "#e6cb8e",
                          fontFamily: "'Koulen', Impact, Arial, sans-serif",
                          fontWeight: 500,
                          letterSpacing: "0.04em",
                          textTransform: "uppercase",
                          textShadow: "0 2px 16px rgba(230, 203, 142, 0.3), 0 1px 2px rgba(0, 0, 0, 0.8)"
                        }}>
                      Episodes
                    </h2>

                    <div className="space-y-4 sm:space-y-6"> {/* Enhanced spacing for mobile */}
                      {/* Season Selector */}
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2 sm:mb-3"> {/* Enhanced mobile margin */}
                          Select Season
                        </label>
                        <Select value={selectedSeasonId} onValueChange={handleSeasonSelect}>
                          <SelectTrigger className="w-full bg-background/50 border-border/50 hover:border-primary/50 transition-colors h-10 sm:h-11"> {/* Enhanced mobile height */}
                            <SelectValue placeholder="Choose a season..." />
                          </SelectTrigger>
                          <SelectContent className="bg-popover border-border/50">
                            {content.seasons.map((season) => (
                              <SelectItem
                                key={season.id}
                                value={season.id}
                                className="hover:bg-primary/10 hover:text-primary transition-colors"
                              >
                                Season {season.season_number || season.seasonNumber}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Episode Selector */}
                      {selectedSeasonId && (
                        <div>
                          <label className="block text-sm font-medium text-foreground mb-2 sm:mb-3"> {/* Enhanced mobile margin */}
                            Select Episode
                          </label>
                          <Select value={selectedEpisodeId} onValueChange={handleEpisodeSelectFromDropdown}>
                            <SelectTrigger className="w-full bg-background/50 border-border/50 hover:border-primary/50 transition-colors h-10 sm:h-11"> {/* Enhanced mobile height */}
                              <SelectValue placeholder="Choose an episode..." />
                            </SelectTrigger>
                            <SelectContent className="bg-popover border-border/50 max-h-60">
                              {content.seasons
                                .find(season => season.id === selectedSeasonId)
                                ?.episodes?.map((episode) => (
                                <SelectItem
                                  key={episode.id}
                                  value={episode.id}
                                  className="hover:bg-primary/10 hover:text-primary transition-colors"
                                  disabled={!episode.secure_video_links && !episode.secureVideoLinks}
                                >
                                  <div className="flex flex-col items-start">
                                    <span className="font-medium">
                                      E{episode.episode_number || episode.episode}: {episode.title}
                                    </span>
                                    {episode.description && (
                                      <span className="text-xs text-muted-foreground line-clamp-1 mt-1">
                                        {episode.description}
                                      </span>
                                    )}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      )}

                      {/* Selected Episode Info */}
                      {selectedEpisodeId && selectedEpisodeTitle && (
                        <div className="mt-4 p-3 sm:p-4 bg-primary/10 border border-primary/20 rounded-lg"> {/* Enhanced mobile padding */}
                          <p className="text-sm font-medium text-primary">
                            Now Playing: {selectedEpisodeTitle}
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Video Player - Now positioned after Episodes section with enhanced spacing */}
              {(hasVideoLinks || selectedEpisodeVideoLinks) && (
                <Card className="content-page-card video-player-container overflow-hidden rounded-xl sm:rounded-2xl border-border/50 shadow-2xl bg-card/95 backdrop-blur-sm mt-6 sm:mt-8 md:mt-10"> {/* Added responsive top margin for proper spacing */}
                  <CardContent className="p-0">
                    {selectedEpisodeTitle && (
                      <div className="bg-primary/10 border-b border-primary/20 px-4 py-2 sm:py-3"> {/* Enhanced mobile padding */}
                        <p className="text-sm font-medium text-primary">
                          Now Playing: {selectedEpisodeTitle}
                        </p>
                      </div>
                    )}
                    <SecureVideoPlayer
                      key={`player-${selectedEpisodeId || 'main'}-${selectedEpisodeVideoLinks || content.secureVideoLinks || content.videoLinks}`}
                      encodedVideoLinks={selectedEpisodeVideoLinks || content.secureVideoLinks}
                      legacyVideoLinks={!selectedEpisodeVideoLinks ? content.videoLinks : undefined}
                      title={selectedEpisodeTitle || content.title}
                      showPlayerSelection={true}
                      className="w-full"
                    />
                  </CardContent>
                </Card>
              )}

              {/* Trailer Section */}
              {content.trailer && (
                <Card className="content-page-card rounded-xl sm:rounded-2xl border-border/50 shadow-xl bg-card/95 backdrop-blur-sm hover:shadow-2xl transition-all duration-300">
                  <CardContent className="p-4 sm:p-6 lg:p-8">
                    <h2 className="stdb-heading text-2xl mb-4" /* Reduced from text-3xl and mb-6 */
                        style={{
                          color: "#e6cb8e",
                          fontFamily: "'Koulen', Impact, Arial, sans-serif",
                          fontWeight: 500, /* Increased from 400 */
                          letterSpacing: "0.04em", /* Reduced from 0.05em */
                          textTransform: "uppercase",
                          textShadow: "0 2px 16px rgba(230, 203, 142, 0.3), 0 1px 2px rgba(0, 0, 0, 0.8)"
                        }}>
                      <Play className="inline-block w-5 h-5 mr-2 mb-1" /> {/* Reduced icon size */}
                      Official Trailer
                    </h2>
                    <div className="aspect-video rounded-lg overflow-hidden bg-black shadow-2xl">
                      <iframe
                        src={content.trailer}
                        title={`${content.title} - Official Trailer`}
                        className="w-full h-full"
                        allowFullScreen
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; fullscreen"
                        loading="lazy"
                        referrerPolicy="no-referrer-when-downgrade"
                      />
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Description */}
              <Card className="content-page-card rounded-xl sm:rounded-2xl border-border/50 shadow-xl bg-card/95 backdrop-blur-sm hover:shadow-2xl transition-all duration-300">
                <CardContent className="p-4 sm:p-6 lg:p-8">
                  <h2 className="stdb-heading text-2xl mb-4" /* Reduced from text-3xl and mb-6 */
                      style={{
                        color: "#e6cb8e",
                        fontFamily: "'Koulen', Impact, Arial, sans-serif",
                        fontWeight: 500, /* Increased from 400 */
                        letterSpacing: "0.04em", /* Reduced from 0.05em */
                        textTransform: "uppercase",
                        textShadow: "0 2px 16px rgba(230, 203, 142, 0.3), 0 1px 2px rgba(0, 0, 0, 0.8)"
                      }}>
                    About
                  </h2>
                  <div className="mb-4">
                    <p className={`text-foreground/90 leading-relaxed text-base font-manrope transition-all duration-300 ${
                      isDescriptionExpanded
                        ? 'line-clamp-none'
                        : 'line-clamp-1 overflow-hidden text-ellipsis'
                    }`}>
                      {content.description}
                    </p>
                    {content.description && content.description.length > 100 && (
                      <button
                        onClick={() => setIsDescriptionExpanded(!isDescriptionExpanded)}
                        className="mt-2 text-primary hover:text-primary/80 text-sm font-medium flex items-center gap-1 transition-colors"
                      >
                        {isDescriptionExpanded ? (
                          <>
                            <ChevronUp className="w-4 h-4" />
                            Show Less
                          </>
                        ) : (
                          <>
                            <ChevronDown className="w-4 h-4" />
                            Read More
                          </>
                        )}
                      </button>
                    )}
                  </div>

                  {content.tags && (
                    <div className="mt-8">
                      <h3 className="font-koulen uppercase tracking-wider text-primary mb-4 text-lg">Tags</h3>
                      <div className="flex flex-wrap gap-3">
                        {content.tags.split(',').map((tag, index) => (
                          <Badge
                            key={index}
                            variant="secondary"
                            className="bg-primary/10 text-primary border-primary/30 hover:bg-primary/20 transition-colors duration-200 px-3 py-1"
                          >
                            {tag.trim()}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-4 sm:space-y-6 lg:space-y-8">
              {/* Details */}
              <Card className="content-page-card rounded-xl sm:rounded-2xl border-border/50 shadow-xl bg-card/95 backdrop-blur-sm hover:shadow-2xl transition-all duration-300">
                <CardContent className="p-4 sm:p-6 lg:p-8">
                  <h3 className="stdb-heading text-xl mb-4" /* Reduced from text-2xl and mb-6 */
                      style={{
                        color: "#e6cb8e",
                        fontFamily: "'Koulen', Impact, Arial, sans-serif",
                        fontWeight: 500, /* Increased from 400 */
                        letterSpacing: "0.04em", /* Reduced from 0.05em */
                        textTransform: "uppercase",
                        textShadow: "0 2px 16px rgba(230, 203, 142, 0.3), 0 1px 2px rgba(0, 0, 0, 0.8)"
                      }}>
                    Details
                  </h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center py-2 border-b border-border/30">
                      <span className="text-muted-foreground font-medium">Type</span>
                      <span className="capitalize font-semibold text-primary">{content.type}</span>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-border/30">
                      <span className="text-muted-foreground font-medium">Year</span>
                      <span className="font-semibold">{content.year}</span>
                    </div>
                    {content.runtime && (
                      <div className="flex justify-between items-center py-2 border-b border-border/30">
                        <span className="text-muted-foreground font-medium">Runtime</span>
                        <span className="font-semibold">{content.runtime} minutes</span>
                      </div>
                    )}
                    {content.studio && (
                      <div className="flex justify-between items-center py-2 border-b border-border/30">
                        <span className="text-muted-foreground font-medium">Studio</span>
                        <span className="font-semibold text-right max-w-[60%]">{content.studio}</span>
                      </div>
                    )}
                    {content.languages && content.languages.length > 0 && (
                      <div className="flex justify-between items-center py-2 border-b border-border/30">
                        <span className="text-muted-foreground font-medium">Languages</span>
                        <span className="font-semibold text-right max-w-[60%]">{content.languages.join(', ')}</span>
                      </div>
                    )}
                    {content.quality && content.quality.length > 0 && (
                      <div className="flex justify-between items-center py-2">
                        <span className="text-muted-foreground font-medium">Quality</span>
                        <div className="flex flex-wrap gap-1 justify-end max-w-[60%]">
                          {content.quality.map((q, index) => (
                            <Badge key={index} variant="secondary" className="bg-primary/10 text-primary border-primary/30 text-xs">
                              {q}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Back Button */}
              <Button
                variant="outline"
                className="w-full rounded-xl border-border/50 hover:border-primary/50 hover:bg-primary/10 hover:text-primary transition-all duration-300 py-3 font-medium"
                onClick={handleGoBack}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Go Back
              </Button>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />

      {/* All Seasons Modal */}
      <AllSeasonsModal
        isOpen={isAllSeasonsModalOpen}
        onClose={() => setIsAllSeasonsModalOpen(false)}
        content={content}
        onEpisodeSelect={handleEpisodeSelect}
      />
    </div>
  );
}
